# GSM功能添加说明

## 概述

已成功为项目添加GSM通信功能，实现了设备启动后同时开启GPS和GSM电源，执行完整的AT指令序列，并发送实时数据到TCP服务器。

## 主要修改内容

### 1. 全局变量添加 (globals.h/globals.c)
- `gsm_ccid[32]`: 存储GSM模块CCID号
- `gsm_signal_quality`: 存储GSM信号质量 (-128表示无效，0-31表示信号强度)

### 2. GSM模块功能增强 (GSM.h/GSM.c)

#### 新增宏定义
- `GSM_POWER_ON_DELAY_MS`: GSM电源开启后等待时间(5秒)
- `GSM_AT_RETRY_COUNT`: AT指令重试次数(3次)

#### 新增函数
- `GSM_PowerOn()`: GSM电源开启
- `GSM_PowerOff()`: GSM电源关闭  
- `GSM_FullInit()`: GSM完整初始化流程
- `GSM_SendATCommandWithRetry()`: 带重试机制的AT指令发送

#### 改进功能
- 改进了CCID号解析算法，支持15-20位数字CCID
- 改进了数据发送函数，增加详细状态输出
- 使用阻塞接收避免115200波特率下的数据丢失
- 所有AT指令响应都会打印输出

### 3. FreeRTOS任务系统 (freertos.c)

#### 新增GSM任务
- 创建独立的GSM任务 (`StartGSMTask`)
- 添加GSM相关信号量 (`gsmStartSemHandle`, `gsmReadySemHandle`)
- GSM任务与GPS、传感器任务并行执行

#### GSM任务执行流程
1. 等待主任务启动信号
2. 开启GSM电源并等待5秒
3. 执行AT指令序列：
   - 关闭回显 (ATE0)
   - 获取CCID号 (AT+CCID)
   - 获取模块电压 (AT+CBC)
   - 查询网络注册状态 (AT+CREG?)
   - 获取信号强度 (AT+CSQ)
4. 连接TCP服务器 (AT+CIPSTART)
5. 设置快发模式 (AT+CIPQSEND=1)
6. 释放就绪信号量

### 4. 主控制流程修改

#### 并行任务启动
- 同时启动GPS、传感器和GSM三个任务
- 等待所有任务完成数据采集和初始化

#### 数据合成改进
- 使用真实的GSM CCID号替换默认的"TEST0001"
- 使用真实的GSM信号强度替换默认的-128

#### 数据发送逻辑
- 如果GSM初始化成功且连接到服务器，通过GSM发送数据
- 无论GSM发送是否成功，都打印数据内容供查看
- 数据仍然缓存到SPI Flash环形缓冲区

## AT指令执行序列

设备启动后的完整AT指令序列：

```
1. GSM电源开启 (RF_PWR_ON)
2. 等待5秒模块启动
3. ATE0 (关闭回显)
4. AT+CCID (获取CCID号)
5. AT+CBC (获取模块电压)
6. AT+CREG? (查询网络注册状态)
7. AT+CSQ (获取信号强度)
8. AT+CIPSTART="TCP","************",48085 (连接TCP服务器)
9. AT+CIPQSEND=1 (设置快发模式)
10. AT+CIPSEND=数据长度 (发送数据)
```

## 重试机制

- 每个AT指令最多重试3次
- 重试间隔1秒
- 连接服务器最多重试2次
- 数据发送最多重试2次

## 错误处理

- 所有AT指令响应都会打印输出
- 失败的指令会显示错误状态码
- GSM初始化失败不会影响GPS和传感器功能
- 即使GSM发送失败，数据仍会打印输出供查看

## 测试要点

1. **电源控制**: 确认GSM电源正确开启
2. **AT指令响应**: 查看所有AT指令的响应内容
3. **CCID获取**: 确认CCID号正确解析并替换默认值
4. **信号强度**: 确认信号强度正确获取并显示
5. **TCP连接**: 确认能够成功连接到服务器
6. **数据发送**: 确认数据能够成功发送到服务器
7. **数据格式**: 确认发送的数据包含真实的CCID和信号强度

## 预期输出示例

```
GSM power ON
Waiting 5000 ms for GSM module startup...
Starting GSM AT command sequence...
AT Command [1/4]: ATE0
AT Response [ATE0]: ATE0\r\nOK\r\n
AT Command [1/4]: AT+CCID
AT Response [AT+CCID]: AT+CCID\r\n89860478101234567890\r\nOK\r\n
CCID obtained: 89860478101234567890
GSM module voltage: 3800 mV
Network registration status: 1
Signal strength: 15
GSM initialization completed
AT Command [1/3]: AT+CIPSTART="TCP","************",48085
TCP server connected successfully
AT Command [1/4]: AT+CIPQSEND=1
Quick send mode enabled
GSM: Signal=15, CCID=89860478101234567890
Sending data via GSM...
Preparing to send 101 bytes of data
Sending data: HY101S+119.96203+30.27613+070438.020725+18.3+1+7+0.4+3.72+29.4+2.9+3.0+-3.6+-10.9+0.0+0.00+15+89860478101234567890+E
Data sent successfully via GSM
```

## 注意事项

1. GSM模块需要插入有效的SIM卡
2. 确保网络信号良好
3. 服务器地址和端口需要可访问
4. 如果某个AT指令失败，会继续执行下一个指令而不关闭电源
5. GSM初始化可能需要30秒左右的时间
