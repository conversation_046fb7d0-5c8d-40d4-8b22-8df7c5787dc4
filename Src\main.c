/* USER CODE BEGIN Header */
/**
  * @file           : main.c
  * @brief          : Main program body
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "rtc.h"
#include "spi.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

//更新日期：2025-1-18
//更新功能：
//1.移除GM20/GSM相关代码，用于测试基础功能；
//2.数据合成使用HYS新协议；
//3.温度值采集使用单片机内部温度；
//4.电池电压与温度校准宏定义在GPS.H文件里；
//5.解决日期错误的BUG；
//6.增加GPS等待时间宏定义；

//当前状态：
//- 已移除所有GM20/GSM模块相关代码
//- 数据发送替换为打印输出
//- 保留GPS、传感器、电源管理等基础功能
//- 待基础功能测试通过后再添加GSM模块功能

//待修复问题：
//1.GPS数据要优先使用高精度定位质量1的数据；
//2.GPS等待超时统一使用宏定义时间，取消代码中固定等待90秒的代码；
//3.修复GPS等待超时后LED常亮BUG，在GPS数据有效后将LED关闭，在设备进入休眠前将LED关闭；



#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "globals.h"
#include "FLASH/bsp_flash.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "GPS.h"
#include "rtc_sync.h"
#include "GSM.h"
#include "network_command.h"

#define UART1_RX_BUFFER_SIZE 1
uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE];
#define GPS_BUFFER_SIZE 256
char gps_buffer[GPS_BUFFER_SIZE];
uint16_t gps_buffer_index = 0;

// LPUART1 (GSM/网络指令) 接收缓冲区
#define LPUART1_RX_BUFFER_SIZE 1
uint8_t lpuart1_rx_buffer[LPUART1_RX_BUFFER_SIZE];

// 网络指令缓冲区
#define NETWORK_CMD_BUFFER_SIZE 32
#define NETWORK_CMD_QUEUE_SIZE 5  // 最多缓存5个指令
char network_cmd_queue[NETWORK_CMD_QUEUE_SIZE][NETWORK_CMD_BUFFER_SIZE];  // 指令队列
uint8_t network_cmd_queue_head = 0;  // 队列头（写入位置）
uint8_t network_cmd_queue_tail = 0;  // 队列尾（读取位置）
uint8_t network_cmd_queue_count = 0; // 队列中指令数量

// GSM响应缓冲区（用于中断接收）
#define GSM_INTERRUPT_BUFFER_SIZE 512
char gsm_interrupt_buffer[GSM_INTERRUPT_BUFFER_SIZE];
uint16_t gsm_interrupt_buffer_index = 0;
uint8_t gsm_response_ready = 0;

#define IS_GPS_PWR_ON() (HAL_GPIO_ReadPin(GPS_PWR_GPIO_Port, GPS_PWR_Pin) == GPIO_PIN_SET)

uint8_t int_wake_src = 0;

LSM6DS3_Data imuData;
LSM6DS3_Attitude attitude;

void PrintCurrentTime(void);
uint8_t Check_Button_Status(void);
void Enter_Sleep_Mode(uint32_t sleep_seconds);
void UART2_SendString(const char *str);
void UART2_SendStatus(uint8_t status_code, const char *short_msg);
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
void Print_Data_String(const char *data_string); // 替代GM20发送的打印函数
void GPS_ParseData(void);

// 网络指令队列处理函数
uint8_t NetworkCmd_HasPendingCommands(void);
uint8_t NetworkCmd_GetNextCommand(char* cmd_buffer, uint16_t buffer_size);

HAL_StatusTypeDef Flash_WriteUint32(uint8_t index, uint32_t value);
HAL_StatusTypeDef Flash_ReadUint32(uint8_t index, uint32_t *value);
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void MX_FREERTOS_Init(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// Print current RTC time
void PrintCurrentTime(void) {
  RTC_TimeTypeDef time;
  RTC_DateTypeDef date;
  RTC_GetDateTime(&time, &date);
}

// External variables
extern GPS_Data_t gps_data;
extern float pw;

// GM20相关函数已移除，用于测试除GSM外的其他功能

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();

  MX_ADC_Init();
  MX_I2C1_Init();
  MX_RTC_Init();
  MX_SPI1_Init();
  MX_LPUART1_UART_Init();  // 启用LPUART1初始化，用于GSM模块通信
  MX_USART4_UART_Init();

  /* USER CODE BEGIN 2 */
  // Initialize UART receive buffers
  memset(uart1_rx_buffer, 0, UART1_RX_BUFFER_SIZE);
  memset(lpuart1_rx_buffer, 0, LPUART1_RX_BUFFER_SIZE);
  gps_buffer_index = 0;

  // 初始化网络指令队列
  network_cmd_queue_head = 0;
  network_cmd_queue_tail = 0;
  network_cmd_queue_count = 0;

  // Start UART receive interrupts
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);  // GPS
  HAL_UART_Receive_IT(&hlpuart1, &lpuart1_rx_buffer[0], 1);  // 网络指令专用中断接收

  // Enable backup domain access
  HAL_PWR_EnableBkUpAccess();

  // Load RTC settings and initialize sync mechanism
  RTC_LoadSettings();
  RTC_Sync_Init();
  PrintCurrentTime();

  // Initialize internal Flash first (required for SPI Flash ring buffer info storage)
  if (FLASH_Init() == HAL_OK) {
    printf("Internal Flash OK\r\n");
  } else {
    printf("Internal Flash FAIL\r\n");
  }

  // Turn on V_OUT power for SPI Flash and sensors
  V_OUT_ON;
  HAL_Delay(500);  // Wait for power stabilization

  // Initialize SPI Flash
	if (SPI_FLASH_Init() == HAL_OK) {
	printf("SPI Flash OK\r\n");
	// Print unread data count
	unsigned long unread_count = SPI_FLASH_GetRecordCount();
	printf("Flash data: %lu unread\r\n", unread_count);
	} else {
	printf("SPI Flash FAIL\r\n");
	}
	//手动读取历史数据
	SPI_FLASH_ReadMultipleRecords(150); // 例如读取150条


  /* USER CODE END 2 */

  /* Call init function for freertos objects (in cmsis_os2.c) */
  MX_FREERTOS_Init();

  /* Start scheduler */
  osKernelStart();

  /* We should never get here as control is now taken by the scheduler */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLLMUL_4;
  RCC_OscInitStruct.PLL.PLLDIV = RCC_PLLDIV_2;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_1) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_I2C1|RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
// Printf redirection to UART
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

// UART receive complete callback
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if(huart->Instance == USART1)  // GPS module
  {
    if (gps_buffer_index < GPS_BUFFER_SIZE - 1) {
      gps_buffer[gps_buffer_index++] = uart1_rx_buffer[0];
      gps_buffer[gps_buffer_index] = '\0';

      // Check for complete NMEA sentence
      if (uart1_rx_buffer[0] == '\n') {
        gps_new_data = 1;
      }
    } else {
      // Buffer full, reset
      gps_buffer_index = 0;
      memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    }

    HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  }
  else if(huart->Instance == LPUART1)  // LPUART1统一中断接收
  {
    uint8_t received_char = lpuart1_rx_buffer[0];

    // 统一接收所有数据到GSM缓冲区
    if (gsm_interrupt_buffer_index < GSM_INTERRUPT_BUFFER_SIZE - 1) {
      gsm_interrupt_buffer[gsm_interrupt_buffer_index++] = received_char;
      gsm_interrupt_buffer[gsm_interrupt_buffer_index] = '\0';

      // 调试：打印接收到的字符（仅对特殊字符）
      if (received_char == '\r' || received_char == '\n' ||
          (gsm_interrupt_buffer_index >= 3 && strstr(gsm_interrupt_buffer, "ZL+") != NULL)) {
        printf("UART RX: 0x%02X ('%c'), buf_len=%d\r\n",
               received_char, (received_char >= 32 && received_char <= 126) ? received_char : '.',
               gsm_interrupt_buffer_index);
      }

      // 检查是否包含网络指令（包含ZL+且以换行符结尾）
      if (received_char == '\n' && gsm_interrupt_buffer_index >= 4 &&
          strstr(gsm_interrupt_buffer, "ZL+") != NULL) {

        printf("Network command detection triggered, buffer content: [%s]\r\n", gsm_interrupt_buffer);

        // 解析缓冲区中的所有ZL+指令
        char* search_pos = gsm_interrupt_buffer;
        char* last_processed_pos = gsm_interrupt_buffer;

        while ((search_pos = strstr(search_pos, "ZL+")) != NULL) {
          // 找到ZL+指令的结束位置（包括\r\n）
          char* end_pos = search_pos;
          while (*end_pos != '\0' && *end_pos != '\r' && *end_pos != '\n') {
            end_pos++;
          }

          // 跳过\r\n字符
          if (*end_pos == '\r') end_pos++;
          if (*end_pos == '\n') end_pos++;

          // 提取单个指令（不包括\r\n）
          char* cmd_end = end_pos;
          while (cmd_end > search_pos && (*(cmd_end-1) == '\r' || *(cmd_end-1) == '\n')) {
            cmd_end--;
          }

          int cmd_len = cmd_end - search_pos;
          if (cmd_len > 0 && cmd_len < NETWORK_CMD_BUFFER_SIZE - 1) {
            // 检查队列是否有空间
            if (network_cmd_queue_count < NETWORK_CMD_QUEUE_SIZE) {
              strncpy(network_cmd_queue[network_cmd_queue_head], search_pos, cmd_len);
              network_cmd_queue[network_cmd_queue_head][cmd_len] = '\0';

              printf("Network command queued: %s\r\n", network_cmd_queue[network_cmd_queue_head]);

              // 更新队列指针
              network_cmd_queue_head = (network_cmd_queue_head + 1) % NETWORK_CMD_QUEUE_SIZE;
              network_cmd_queue_count++;
            } else {
              printf("Network command queue full, dropping: %.*s\r\n", cmd_len, search_pos);
            }
          }

          // 记录已处理的位置
          last_processed_pos = end_pos;
          // 继续搜索下一个指令
          search_pos = end_pos;
        }

        // 移除已处理的数据，保留未处理的部分
        if (last_processed_pos > gsm_interrupt_buffer) {
          uint16_t remaining_len = gsm_interrupt_buffer + gsm_interrupt_buffer_index - last_processed_pos;
          printf("Buffer processing: processed_pos_offset=%d, remaining_len=%d\r\n",
                 (int)(last_processed_pos - gsm_interrupt_buffer), remaining_len);
          if (remaining_len > 0) {
            memmove(gsm_interrupt_buffer, last_processed_pos, remaining_len);
            gsm_interrupt_buffer_index = remaining_len;
            gsm_interrupt_buffer[gsm_interrupt_buffer_index] = '\0';
            printf("Buffer after processing: [%s], new_len=%d\r\n", gsm_interrupt_buffer, gsm_interrupt_buffer_index);
          } else {
            // 没有剩余数据，清空缓冲区
            gsm_interrupt_buffer_index = 0;
            gsm_response_ready = 0;
            memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);
            printf("Buffer cleared after processing\r\n");
          }
        } else {
          printf("No data processed, buffer unchanged\r\n");
        }
      }
      // 检查AT指令响应结束标志
      else {
        uint16_t len = gsm_interrupt_buffer_index;

        // 检查常见的AT指令响应结束标志
        if ((len >= 4 && strcmp(&gsm_interrupt_buffer[len-4], "OK\r\n") == 0) ||
            (len >= 7 && strcmp(&gsm_interrupt_buffer[len-7], "ERROR\r\n") == 0) ||
            (len >= 6 && strcmp(&gsm_interrupt_buffer[len-6], "FAIL\r\n") == 0) ||
            (len >= 12 && strcmp(&gsm_interrupt_buffer[len-12], "CONNECT OK\r\n") == 0) ||
            (len >= 9 && strcmp(&gsm_interrupt_buffer[len-9], "SEND OK\r\n") == 0) ||
            (len >= 10 && strcmp(&gsm_interrupt_buffer[len-10], "CLOSE OK\r\n") == 0) ||
            (len >= 8 && strcmp(&gsm_interrupt_buffer[len-8], "CLOSED\r\n") == 0) ||
            (len >= 8 && strcmp(&gsm_interrupt_buffer[len-8], "ACCEPT\r\n") == 0) ||
            (strstr(gsm_interrupt_buffer, "DATA ACCEPT:") != NULL && received_char == '\n')) {
          gsm_response_ready = 1;
        }
        // 特殊处理：'>'字符（数据发送提示符）
        else if (received_char == '>') {
          gsm_response_ready = 1;
        }
      }
    } else {
      // 缓冲区满，重置
      gsm_interrupt_buffer_index = 0;
      gsm_response_ready = 0;
      memset(gsm_interrupt_buffer, 0, GSM_INTERRUPT_BUFFER_SIZE);
    }

    HAL_UART_Receive_IT(&hlpuart1, &lpuart1_rx_buffer[0], 1);
  }
}

// Date validation structure for continuity checking
typedef struct {
    uint8_t last_valid_day;
    uint8_t last_valid_month;
    uint8_t last_valid_year;
    uint8_t last_valid_hour;
    uint32_t last_timestamp;
    uint8_t consecutive_count;
} DateValidation_t;

static DateValidation_t date_validator = {0};

// Global variables for backup date storage
static uint8_t backup_valid_day = 0;
static uint8_t backup_valid_month = 0;
static uint8_t backup_valid_year = 0;
static uint8_t backup_date_initialized = 0;

// Function to detect specific 040625 error
uint8_t detect_040625_error(uint8_t day, uint8_t month, uint8_t year)
{
    // Check for the specific 04/06/25 combination
    if (day == 4 && month == 6 && year == 25) {
        return 1; // 040625 error detected
    }
    return 0;
}

// Function to check if date is reasonable (not RTC initialization default)
uint8_t is_date_reasonable(uint8_t day, uint8_t month, uint8_t year)
{
    // Check if it's the RTC initialization default date (01/01/25)
    if (day == 1 && month == 1 && year == 25) {
        return 0; // This is likely RTC initialization default, not reasonable
    }

    // Check basic date validity
    if (year < 20 || year > 30 || month < 1 || month > 12 || day < 1 || day > 31) {
        return 0; // Invalid date format
    }

    return 1; // Date seems reasonable
}

// Function to validate date continuity
uint8_t validate_date_continuity(uint8_t day, uint8_t month, uint8_t year, uint8_t hour)
{
    uint32_t current_timestamp = HAL_GetTick();

    // First time initialization - only accept reasonable dates
    if (date_validator.consecutive_count == 0) {
        if (is_date_reasonable(day, month, year)) {
            date_validator.last_valid_day = day;
            date_validator.last_valid_month = month;
            date_validator.last_valid_year = year;
            date_validator.last_valid_hour = hour;
            date_validator.last_timestamp = current_timestamp;
            date_validator.consecutive_count = 1;
            printf("First reasonable date accepted: %02d/%02d/%02d\r\n", day, month, year);
            return 1; // Accept first reasonable date
        } else {
            printf("First date rejected as unreasonable: %02d/%02d/%02d\r\n", day, month, year);
            return 0; // Reject unreasonable first date
        }
    }

    // Calculate time difference in hours (approximate)
    uint32_t time_diff_ms = current_timestamp - date_validator.last_timestamp;
    uint32_t time_diff_hours = time_diff_ms / (1000 * 60 * 60);

    // Check for reasonable date progression
    int day_diff = day - date_validator.last_valid_day;
    int month_diff = month - date_validator.last_valid_month;
    int year_diff = year - date_validator.last_valid_year;

    // Handle month rollover
    if (day_diff < 0 && month_diff == 1) {
        day_diff += 31; // Approximate month rollover
    }

    // Check for midnight rollover (hour 23->0-2 with day+1)
    uint8_t is_midnight_rollover = 0;
    if ((date_validator.last_valid_hour >= 22) && (hour <= 2) && (day_diff == 1)) {
        is_midnight_rollover = 1;
    }

    // Validate date change is reasonable
    if (year_diff == 0 && month_diff == 0 && abs(day_diff) <= 1) {
        // Same month, day difference <= 1: reasonable
        return 1;
    } else if (year_diff == 0 && month_diff == 1 && day_diff <= 31 && day_diff >= -31) {
        // Month rollover: reasonable
        return 1;
    } else if (is_midnight_rollover) {
        // Midnight rollover: reasonable
        return 1;
    } else if (time_diff_hours > 48 && abs(day_diff) <= 3) {
        // Long time gap with small date change: might be reasonable
        return 1;
    }

    return 0; // Date change seems unreasonable
}

// Function to save valid date to global backup variables
void save_valid_date_to_backup(uint8_t day, uint8_t month, uint8_t year)
{
    backup_valid_day = day;
    backup_valid_month = month;
    backup_valid_year = year;
    backup_date_initialized = 1;

//    printf("Backup date saved: %02d/%02d/%02d\r\n", day, month, year);
}

// Function to restore date from global backup variables
uint8_t restore_date_from_backup(uint8_t *day, uint8_t *month, uint8_t *year)
{
    if (backup_date_initialized && backup_valid_year >= 20 &&
        backup_valid_month >= 1 && backup_valid_month <= 12 &&
        backup_valid_day >= 1 && backup_valid_day <= 31) {

        *day = backup_valid_day;
        *month = backup_valid_month;
        *year = backup_valid_year;

        printf("Date restored from backup: %02d/%02d/%02d\r\n", *day, *month, *year);
        return 1; // Valid backup date found
    }

    printf("No valid backup date available\r\n");
    return 0; // No valid backup date
}

// Create complete data string for transmission
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size)
{
    char time_str[20] = {0};
    char sn_buffer[32] = {0};
    int8_t signal_quality = -128;

    // 使用GSM模块的真实信号值和CCID号
    strcpy(sn_buffer, gsm_ccid);    // 使用GSM模块的CCID号
    signal_quality = gsm_signal_quality;  // 使用GSM模块的信号质量

    // Step 1: Get GPS and RTC original time/date
    uint8_t gps_time_valid = (gps_data.valid && gps_data.hour <= 23 &&
                             gps_data.minute <= 59 && gps_data.second <= 59);
    uint8_t gps_date_valid = (gps_data.year >= 2020 && gps_data.month >= 1 &&
                             gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31);

    // Get RTC time as primary source when GPS invalid
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    RTC_GetDateTime(&rtc_time, &rtc_date);

    // Step 2 & 3: Date validation and error detection
    uint8_t final_day, final_month, final_year;
    uint8_t final_hour, final_minute, final_second;
    uint8_t date_source_gps = 0; // 0=RTC, 1=GPS

    // Determine initial time/date source based on GPS validity
    if (gps_data.valid && gps_data.latitude_decimal > 0 && gps_data.longitude_decimal > 0) {
        if (gps_time_valid && gps_date_valid) {
            // Use complete GPS time when GPS has valid positioning
            final_hour = gps_data.hour;
            final_minute = gps_data.minute;
            final_second = gps_data.second;
            final_day = gps_data.day;
            final_month = gps_data.month;
            final_year = gps_data.year % 100;
            date_source_gps = 1;
        } else if (gps_time_valid && !gps_date_valid) {
            // Use GPS time + RTC date when GPS time valid but date invalid
            final_hour = gps_data.hour;
            final_minute = gps_data.minute;
            final_second = gps_data.second;
            final_day = rtc_date.Date;
            final_month = rtc_date.Month;
            final_year = rtc_date.Year;
            date_source_gps = 0;
        } else {
            // GPS positioning valid but time invalid, use RTC time
            final_hour = rtc_time.Hours;
            final_minute = rtc_time.Minutes;
            final_second = rtc_time.Seconds;
            final_day = rtc_date.Date;
            final_month = rtc_date.Month;
            final_year = rtc_date.Year;
            date_source_gps = 0;
        }
    } else {
        // GPS invalid or no positioning, always use RTC time and date
        final_hour = rtc_time.Hours;
        final_minute = rtc_time.Minutes;
        final_second = rtc_time.Seconds;
        final_day = rtc_date.Date;
        final_month = rtc_date.Month;
        final_year = rtc_date.Year;
        date_source_gps = 0;
    }

    // Step 2: Execute 040625 specific error detection
    if (detect_040625_error(final_day, final_month, final_year)) {
        printf("040625 error detected! Attempting recovery...\r\n");

        // Step 4: Try to restore from backup
        if (restore_date_from_backup(&final_day, &final_month, &final_year)) {
            printf("Date restored from backup: %02d/%02d/%02d\r\n", final_day, final_month, final_year);
        } else {
            printf("No valid backup date found, using safe default date\r\n");
            // Use a safe default date if backup is also invalid
            final_day = 1;
            final_month = 1;
            final_year = 25; // 2025
        }
    }

    // Step 3: Execute date continuity validation (skip if no previous valid date)
    if (date_validator.consecutive_count > 0) {
        if (!validate_date_continuity(final_day, final_month, final_year, final_hour)) {
            printf("Date continuity validation failed! Current: %02d/%02d/%02d, attempting recovery...\r\n",
                   final_day, final_month, final_year);

            // Step 4: Try to restore from backup
            if (restore_date_from_backup(&final_day, &final_month, &final_year)) {
                printf("Date restored from backup: %02d/%02d/%02d\r\n", final_day, final_month, final_year);
            } else {
                printf("No valid backup date found, using safe default date\r\n");
                // Use a safe default date if backup is also invalid
                final_day = 1;
                final_month = 1;
                final_year = 25; // 2025
            }
        }
    } else {
        // First time running - try to establish first valid date
        if (!validate_date_continuity(final_day, final_month, final_year, final_hour)) {

            // Keep using current date but don't save as backup
            // This allows the system to continue working while waiting for valid GPS
        }
    }

    // Check if validation passed (either continuity check passed or first time accepted)
    if ((date_validator.consecutive_count > 0 && validate_date_continuity(final_day, final_month, final_year, final_hour)) ||
        (date_validator.consecutive_count == 0 && is_date_reasonable(final_day, final_month, final_year))) {
        // Step 6: Update backup only when validation passes AND date is reasonable
        // Additional check: only save backup if GPS is valid or we already have a reasonable backup
        uint8_t should_save_backup = 0;

        if (date_source_gps == 1) {
            // GPS source - always save if validation passed
            should_save_backup = 1;

        } else if (backup_date_initialized) {
            // RTC source but we already have a backup - update it
            should_save_backup = 1;
            printf("RTC date validated, updating existing backup\r\n");
        } else if (is_date_reasonable(final_day, final_month, final_year)) {
            // RTC source, no backup yet, but date seems reasonable
            should_save_backup = 1;
            printf("RTC date seems reasonable, creating first backup\r\n");
        } else {
            // RTC source, no backup, and date seems unreasonable (likely initialization default)
            printf("RTC date unreasonable, not saving as backup: %02d/%02d/%02d\r\n",
                   final_day, final_month, final_year);
        }

        if (should_save_backup) {
            save_valid_date_to_backup(final_day, final_month, final_year);
        }

        // Update validator state
        date_validator.last_valid_day = final_day;
        date_validator.last_valid_month = final_month;
        date_validator.last_valid_year = final_year;
        date_validator.last_valid_hour = final_hour;
        date_validator.last_timestamp = HAL_GetTick();
        date_validator.consecutive_count++;
    }

    // Step 7: Continue with original data synthesis logic
    // Format the validated time string
    snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
            final_hour, final_minute, final_second,
            final_day, final_month, final_year);

    // GPS data or default values - 使用转换后的十进制格式
    float longitude = gps_data.longitude_decimal;
    float latitude = gps_data.latitude_decimal;
    float altitude = gps_data.altitude;
    uint8_t fix_quality = gps_data.fix_quality;
    uint8_t satellites = gps_data.satellites;
    float speed = gps_data.speed;
    float hdop = gps_data.hdop;
    float pdop = gps_data.pdop;
    float course = gps_data.course;

    // 添加GPS状态调试输出
//    printf("GPS debug: valid=%d, quality=%d, satellites=%d, hdop=%.1f\r\n",
//           gps_data.valid, fix_quality, satellites, hdop);

    if (!gps_data.valid) {
        // Use default values when GPS is invalid
        longitude = 0.0;
        latitude = 0.0;
        altitude = 0.0;
        fix_quality = 0;
        satellites = 0;
        speed = 0.0;
        hdop = 99.9;
        pdop = 99.9;
        course = 0.0;
    } else {
        // GPS valid but check for missing precision data
        if (hdop <= 0.0) {
            hdop = 2.5;  // Use reasonable default when HDOP not available
        }
        if (pdop <= 0.0 || pdop >= 99.0) {
            pdop = 3.0;  // Use reasonable default when PDOP not available or invalid
        }
    }

    // Get MCU internal temperature
    extern float mcu_temp;

    // Create data content string
    char data_content[200];
    snprintf(data_content, sizeof(data_content),
            "S+%.5f+%.5f+%s+%.1f+%d+%d+%.1f+%.2f+%.1f+%.1f+%.1f+%.1f+%.1f+%.1f+%.2f+%d+%s+E",
            longitude,
            latitude,
            time_str,
            altitude,
            fix_quality,
            satellites,
            speed,
            pw,
            mcu_temp,  // 使用MCU内部温度替换三轴传感器温度
            hdop,
            pdop,
            attitude.roll,
            attitude.pitch,
            attitude.yaw,
            course,
            signal_quality,
            sn_buffer
            );

    // Build complete string with length header
    uint16_t data_length = strlen(data_content);
    if (data_length < 100) {
        snprintf(output_buffer, buffer_size, "HY0%d%s", data_length, data_content);
    } else {
        snprintf(output_buffer, buffer_size, "HY%d%s", data_length, data_content);
    }

    return HAL_OK;
}

// 打印数据字符串（替代GM20模块发送，用于测试基础功能）
void Print_Data_String(const char *data_string)
{
    uint16_t data_length = strlen(data_string);

    printf("=== Data Output (Replacing GSM Send) ===\r\n");
    printf("Data Length: %d bytes\r\n", data_length);
    printf("Data Content: %s\r\n", data_string);
    printf("=====================================\r\n");
}

// Parse GPS data from buffer
void GPS_ParseData(void)
{
    char *line_start = gps_buffer;
    char *line_end;

    // Find complete NMEA sentences
    while ((line_end = strchr(line_start, '\n')) != NULL) {
        *line_end = '\0';  // Temporarily terminate string

        // Parse NMEA sentence
        if (strlen(line_start) > 6) {
            GPS_ParseNMEA(line_start, &gps_data);
        }

        line_start = line_end + 1;
    }

    // Move remaining data to buffer start
    if (line_start < gps_buffer + gps_buffer_index) {
        uint16_t remaining = gps_buffer + gps_buffer_index - line_start;
        memmove(gps_buffer, line_start, remaining);
        gps_buffer_index = remaining;
        gps_buffer[gps_buffer_index] = '\0';
    } else {
        // No remaining data, clear buffer
        gps_buffer_index = 0;
        gps_buffer[0] = '\0';
    }
}
/* USER CODE END 4 */

/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */

  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM6)
  {
    HAL_IncTick();
  }
  /* USER CODE BEGIN Callback 1 */

  /* USER CODE END Callback 1 */
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  // User can add his own implementation to report the HAL error return state
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  // User can add his own implementation to report the file name and line number
  // ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line)
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/**
 * @brief  检查是否有待处理的网络指令
 * @retval 1: 有待处理指令, 0: 无待处理指令
 */
uint8_t NetworkCmd_HasPendingCommands(void)
{
  return (network_cmd_queue_count > 0) ? 1 : 0;
}

/**
 * @brief  从队列中获取下一个网络指令
 * @param  cmd_buffer: 指令缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval 1: 成功获取指令, 0: 队列为空
 */
uint8_t NetworkCmd_GetNextCommand(char* cmd_buffer, uint16_t buffer_size)
{
  if (network_cmd_queue_count == 0) {
    return 0;  // 队列为空
  }

  // 复制指令到缓冲区
  strncpy(cmd_buffer, network_cmd_queue[network_cmd_queue_tail], buffer_size - 1);
  cmd_buffer[buffer_size - 1] = '\0';

  // 更新队列指针
  network_cmd_queue_tail = (network_cmd_queue_tail + 1) % NETWORK_CMD_QUEUE_SIZE;
  network_cmd_queue_count--;

  return 1;  // 成功获取指令
}
