/**
  ******************************************************************************
  * @file    GPS.c
  * @brief   GPS module driver source file
  ******************************************************************************
  */

#include "GPS.h"

// Global variables
GPS_Data_t gps_data;
Course_Data_t course_data;
uint8_t gps_data_ready = 0;
uint8_t gps_new_data = 0;
char gps_nmea_buffer[256]; // Store NMEA sentences to avoid circular overwrite

// GPS临时存储变量 - 保留用于兼容性，但不再使用
GPS_TempData_t gps_temp_data = {0};

// GPS module initialization
void GPS_Init(void)
{
    // Clear GPS data structure completely
    memset(&gps_data, 0, sizeof(GPS_Data_t));

    // Initialize with current RTC date/time instead of hardcoded defaults
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

    // Set GPS default values from current RTC (only time/date, not position)
    gps_data.year = 2000 + rtc_date.Year;
    gps_data.month = rtc_date.Month;
    gps_data.day = rtc_date.Date;
    gps_data.hour = rtc_time.Hours;
    gps_data.minute = rtc_time.Minutes;
    gps_data.second = rtc_time.Seconds;

    // 确保位置数据为0
    gps_data.latitude = 0.0;
    gps_data.longitude = 0.0;
    gps_data.latitude_decimal = 0.0;
    gps_data.longitude_decimal = 0.0;
    gps_data.altitude = 0.0;
    gps_data.fix_quality = 0;
    gps_data.satellites = 0;
    gps_data.speed = 0.0;
    gps_data.course = 0.0;
    gps_data.hdop = 0.0;
    gps_data.pdop = 0.0;
    gps_data.vdop = 0.0;
    gps_data.valid = 0;

    // Clear NMEA buffer
    memset(gps_nmea_buffer, 0, sizeof(gps_nmea_buffer));

    // Initialize flags
    gps_data_ready = 0;
    gps_new_data = 0;

    // Initialize course data
    GPS_CourseInit();

    // Initialize GPS temporary storage (保留用于兼容性)
    memset(&gps_temp_data, 0, sizeof(GPS_TempData_t));


}

// Initialize course data
void GPS_CourseInit(void)
{
    // Clear course data structure
    memset(&course_data, 0, sizeof(Course_Data_t));

    // Initialize course history data
    for (int i = 0; i < COURSE_HISTORY_SIZE; i++) {
        course_data.course_history[i] = 0.0f;
    }

    course_data.history_count = 0;
    course_data.history_index = 0;
    course_data.last_valid_course = 0.0f;
    course_data.has_valid_course = 0;
}

// Process NMEA data
void GPS_Process(char *nmea_data)
{
    if (GPS_ParseNMEA(nmea_data, &gps_data)) {
        gps_data.updated = 1;
    }
}

// Parse NMEA data
uint8_t GPS_ParseNMEA(char *nmea_data, GPS_Data_t *gps_data)
{
    char *token;
    char buffer[128];
    uint8_t field_count = 0;
    uint8_t result = 0;

    // 简化：使用临时有效性标志，避免GGA和RMC相互覆盖（参考副本版本）
    uint8_t gga_valid = 0;
    uint8_t rmc_valid = 0;

    // Check input parameter validity
    if (nmea_data == NULL || gps_data == NULL) {
        return 0;
    }

    // Copy data to buffer to avoid modifying original data
    strncpy(buffer, nmea_data, sizeof(buffer) - 1);
    buffer[sizeof(buffer) - 1] = '\0';

    // Get first field
    token = strtok(buffer, ",");
    if (token == NULL) {
        return 0;
    }

    // Parse GGA sentence - Global Positioning System fix data
    if (strcmp(token, "$GNGGA") == 0 || strcmp(token, "$GPGGA") == 0) {
        // Save original GGA sentence
        strncpy(gps_data->gga, nmea_data, sizeof(gps_data->gga) - 1);
        gps_data->gga[sizeof(gps_data->gga) - 1] = '\0';

        field_count = 1;
        while ((token = strtok(NULL, ",")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Latitude
                    if (strlen(token) > 0) {
                        gps_data->latitude = atof(token);
                    }
                    break;
                case 3: // North/South hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ns = token[0];
                    }
                    break;
                case 4: // Longitude
                    if (strlen(token) > 0) {
                        gps_data->longitude = atof(token);
                    }
                    break;
                case 5: // East/West hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ew = token[0];
                    }
                    break;
                case 6: // Fix quality
                    if (strlen(token) > 0) {
                        gps_data->fix_quality = atoi(token);
                        // 简化：设置GGA有效性标志，但不直接修改gps_data->valid（参考副本版本）
                        if (gps_data->fix_quality > 0) {
                            gga_valid = 1;
                        } else {
                            gga_valid = 0;
                        }
                    }
                    break;
                case 7: // Number of satellites used
                    if (strlen(token) > 0) {
                        gps_data->satellites = atoi(token);
                    }
                    break;
                case 8: // Horizontal dilution of precision
                    if (strlen(token) > 0) {
                        gps_data->hdop = atof(token);
                    }
                    break;
                case 9: // Altitude
                    if (strlen(token) > 0) {
                        gps_data->altitude = atof(token);
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }

        // Calculate decimal degree format coordinates
        if (gps_data->latitude > 0 && gps_data->longitude > 0) {
            gps_data->latitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->latitude, gps_data->ns);
            gps_data->longitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->longitude, gps_data->ew);
        }



        result = 1;
    }
    // Parse RMC sentence - Recommended minimum positioning information
    else if (strcmp(token, "$GNRMC") == 0 || strcmp(token, "$GPRMC") == 0) {
        // Save original RMC sentence
        strncpy(gps_data->rmc, nmea_data, sizeof(gps_data->rmc) - 1);
        gps_data->rmc[sizeof(gps_data->rmc) - 1] = '\0';

        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Status A=valid, V=invalid
                    if (strlen(token) > 0) {
                        // 简化：设置RMC有效性标志（参考副本版本）
                        rmc_valid = (token[0] == 'A') ? 1 : 0;
                    }
                    break;
                case 3: // Latitude
                    if (strlen(token) > 0) {
                        gps_data->latitude = atof(token);
                    }
                    break;
                case 4: // North/South hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ns = token[0];
                    }
                    break;
                case 5: // Longitude
                    if (strlen(token) > 0) {
                        gps_data->longitude = atof(token);
                    }
                    break;
                case 6: // East/West hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ew = token[0];
                    }
                    break;
                case 7: // Speed (knots)
                    if (strlen(token) > 0) {
                        gps_data->speed = atof(token);
                    }
                    break;
                case 8: // Course (degrees)
                    if (strlen(token) > 0) {
                        gps_data->course = atof(token);
                    }
                    break;
                case 9: // Date (DDMMYY)
                    if (strlen(token) >= 6) {
                        // Parse date with validity check
                        uint8_t day = (token[0] - '0') * 10 + (token[1] - '0');
                        uint8_t month = (token[2] - '0') * 10 + (token[3] - '0');
                        uint16_t year = 2000 + (token[4] - '0') * 10 + (token[5] - '0');

                        // Only update GPS data when parsed date is valid
                        if (year >= 2020 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                            gps_data->day = day;
                            gps_data->month = month;
                            gps_data->year = year;
                        }
                        // If date is invalid, keep original values unchanged
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }

        // Calculate decimal degree format coordinates
        if (gps_data->latitude > 0 && gps_data->longitude > 0) {
            gps_data->latitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->latitude, gps_data->ns);
            gps_data->longitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->longitude, gps_data->ew);
        }

        result = 1;
    }

    // Parse GSA sentence - Satellite status
    else if (strcmp(token, "$GNGSA") == 0 || strcmp(token, "$GPGSA") == 0 || strcmp(token, "$BDGSA") == 0) {
        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            // GSA sentence contains precision factor fields: PDOP, HDOP, VDOP
            if (field_count == 15) { // PDOP
                if (strlen(token) > 0) {
                    gps_data->pdop = atof(token);
                }
            } else if (field_count == 16) { // HDOP
                if (strlen(token) > 0) {
                    // Only update if GGA sentence didn't provide HDOP
                    if (gps_data->hdop == 0.0f) {
                        gps_data->hdop = atof(token);
                    }
                }
            } else if (field_count == 17) { // VDOP
                if (strlen(token) > 0) {
                    gps_data->vdop = atof(token);
                }
            }
            field_count++;
        }
        result = 1;
    }
    // Parse ZDA sentence - Date and time
    else if (strcmp(token, "$GNZDA") == 0 || strcmp(token, "$GPZDA") == 0) {
        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Day
                    if (strlen(token) > 0) {
                        gps_data->day = atoi(token);
                    }
                    break;
                case 3: // Month
                    if (strlen(token) > 0) {
                        gps_data->month = atoi(token);
                    }
                    break;
                case 4: // Year
                    if (strlen(token) > 0) {
                        gps_data->year = atoi(token);
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }
        result = 1;
    }

    // 简化：统一计算最终的有效性状态（参考副本版本）
    // 优先使用GGA的有效性判断，如果GGA无效但RMC有效，则使用RMC
    if (gga_valid) {
        gps_data->valid = 1;
    } else if (rmc_valid) {
        gps_data->valid = 1;
    } else {
        gps_data->valid = 0;
    }

    return result;
}

// Convert NMEA format coordinates to decimal degrees format
float GPS_ConvertToDecimalDegrees(float nmea_format, char direction)
{
    int degrees = (int)(nmea_format / 100);
    float minutes = nmea_format - degrees * 100;
    float decimal_degrees = degrees + minutes / 60.0f;

    // Southern and western hemispheres are negative
    if (direction == 'S' || direction == 'W') {
        decimal_degrees = -decimal_degrees;
    }

    return decimal_degrees;
}

// Print GPS information
void GPS_PrintInfo(GPS_Data_t *gps_data)
{
    if (gps_data == NULL) {
        return;
    }

    // 函数体为空，调试输出已移除
}

// Synchronize RTC clock
uint8_t GPS_SyncRTC(GPS_Data_t *gps_data)
{
    if (gps_data == NULL || !gps_data->valid) {
        return 0;
    }

    // Smart GPS time sync: when GPS has positioning info, time is definitely valid
    // Check if time is valid
    uint8_t time_valid = (gps_data->hour <= 23 && gps_data->minute <= 59 && gps_data->second <= 59);
    if (!time_valid) {
        return 0;
    }

    // Check if date is valid
    uint8_t date_valid = (gps_data->year >= 2020 && gps_data->month >= 1 &&
                         gps_data->month <= 12 && gps_data->day >= 1 && gps_data->day <= 31);
    if (!date_valid) {
        // GPS date invalid but time valid, sync time only, keep RTC date
        RTC_TimeTypeDef rtc_time;
        RTC_DateTypeDef rtc_date;
        HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
        HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

        return RTC_SetDateTime(gps_data->hour, gps_data->minute, gps_data->second,
                              rtc_date.Date, rtc_date.Month, rtc_date.Year) == HAL_OK ? 1 : 0;
    }

    // GPS time and date both valid, perform complete sync
    if (RTC_SetDateTime(gps_data->hour, gps_data->minute, gps_data->second,
                        gps_data->day, gps_data->month, gps_data->year % 100) == HAL_OK) {
        return 1;
    } else {
        return 0;
    }
}

// Get position string for data transmission
char* GPS_GetPositionString(GPS_Data_t *gps_data, char *buffer)
{
    if (gps_data == NULL || buffer == NULL || !gps_data->valid) {
        return NULL;
    }

    // Format position information as string, suitable for GM20 module transmission
    sprintf(buffer, "+%.5f+%.5f+%.1f+%d+%.2f",
            gps_data->latitude,
            gps_data->longitude,
            gps_data->altitude,
            gps_data->satellites,
            gps_data->hdop);

    return buffer;
}

// Process course data
float GPS_ProcessCourse(float course, float speed_kmh)
{
    // If speed is below threshold, use last valid course
    if (speed_kmh < COURSE_SPEED_THRESHOLD) {
        return course_data.has_valid_course ? course_data.last_valid_course : 0.0f;
    }

    // Speed above threshold, current course is valid
    course_data.has_valid_course = 1;
    course_data.last_valid_course = course;

    // Add to history data
    course_data.course_history[course_data.history_index] = course;
    course_data.history_index = (course_data.history_index + 1) % COURSE_HISTORY_SIZE;
    if (course_data.history_count < COURSE_HISTORY_SIZE) {
        course_data.history_count++;
    }

    // Return smoothed course
    return GPS_GetSmoothedCourse();
}

// Get smoothed course value
float GPS_GetSmoothedCourse(void)
{
    // If not enough history data, return last valid course directly
    if (course_data.history_count < 2) {
        return course_data.last_valid_course;
    }

    // Calculate course average (need special handling for angle circularity)
    float sin_sum = 0.0f;
    float cos_sum = 0.0f;

    for (int i = 0; i < course_data.history_count; i++) {
        // Convert angle to radians
        float angle_rad = course_data.course_history[i] * 3.14159f / 180.0f;
        sin_sum += sinf(angle_rad);
        cos_sum += cosf(angle_rad);
    }

    // Calculate average direction (radians)
    float avg_angle_rad = atan2f(sin_sum, cos_sum);

    // Convert back to degrees, ensure in 0-360 range
    float avg_angle_deg = avg_angle_rad * 180.0f / 3.14159f;
    if (avg_angle_deg < 0) {
        avg_angle_deg += 360.0f;
    }

    return avg_angle_deg;
}

// Handle new GPS data
void GPS_HandleNewData(char *nmea_data)
{
    if (nmea_data == NULL || strlen(nmea_data) < 10 || nmea_data[0] != '$') {
        return;
    }

    // Copy NMEA sentence to global buffer
    strcpy(gps_nmea_buffer, nmea_data);
    gps_new_data = 1;  // Set new data flag

    // Process GPS data
    GPS_Process(nmea_data);

    // Set data ready flag
    gps_data_ready = 1;
}

// Update GPS data (process course)
void GPS_UpdateData(void)
{
    if (gps_new_data && gps_data.valid) {
        // Process course data
        float speed_kmh = gps_data.speed * 1.852f;
        float processed_course = GPS_ProcessCourse(gps_data.course, speed_kmh);

        // Update course value in GPS data structure to processed value
        gps_data.course = processed_course;

        // Clear new data flag
        gps_new_data = 0;
    }
}

// Check if GPS data is ready
uint8_t GPS_IsDataReady(void)
{
    return gps_data_ready;
}

// Clear GPS data ready flag
void GPS_ClearDataReadyFlag(void)
{
    gps_data_ready = 0;
    gps_new_data = 0;
}

// Check if quality timeout has occurred (保留用于兼容性，但不再使用)
uint8_t GPS_CheckQualityTimeout(uint32_t timeout_ms)
{
    // 简化版本：不再使用临时数据机制
    return 0; // No timeout
}

// Use temporary data if available (保留用于兼容性，但不再使用)
void GPS_UseTempDataIfNeeded(void)
{
    // 简化版本：不再使用临时数据机制
}

