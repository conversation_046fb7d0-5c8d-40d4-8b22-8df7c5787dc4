/**
  ******************************************************************************
  * @file    GPS.h
  * @brief   GPS模块头文件
  ******************************************************************************
  */

#ifndef __GPS_H
#define __GPS_H

#ifdef __cplusplus
extern "C" {
#endif

/* 包含头文件 */
#include "main.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "rtc.h"
#include "gpio.h"  // 包含GPIO宏定义，用于电源控制等
#include "globals.h"

/* 航向处理相关宏定义 */
#define COURSE_SPEED_THRESHOLD 5.0f  // 航向有效的速度阈值(公里/小时)
#define COURSE_HISTORY_SIZE    5     // 航向历史数据大小(用于平滑处理)

/* 定义GPS数据结构体 */
typedef struct {
    /* 时间信息 */
    uint8_t hour;        // 小时 (UTC)
    uint8_t minute;      // 分钟
    uint8_t second;      // 秒
    uint16_t millisecond; // 毫秒

    uint8_t day;         // 日
    uint8_t month;       // 月
    uint16_t year;       // 年

    /* 位置信息 */
    float latitude;      // 纬度 (NMEA格式: 度分.分)
    char ns;             // 北/南半球 (N/S)
    float longitude;     // 经度 (NMEA格式: 度分.分)
    char ew;             // 东/西半球 (E/W)

    /* 转换后的十进制度格式 */
    float latitude_decimal;  // 纬度 (十进制度)
    float longitude_decimal; // 经度 (十进制度)

    /* 定位信息 */
    uint8_t fix_quality;  // 定位质量 (0=无效, 1=GPS定位, 2=差分GPS, 6=估算)
    uint8_t satellites;   // 使用的卫星数量
    float pdop;           // 位置精度因子
    float hdop;           // 水平精度因子
    float vdop;           // 垂直精度因子
    float altitude;       // 海拔高度 (米)
    float speed;          // 速度 (节)
    float course;         // 航向 (度)

    /* 状态信息 */
    uint8_t valid;        // 数据是否有效 (0=无效, 1=有效)
    uint8_t updated;      // 数据是否已更新 (0=未更新, 1=已更新)

    /* 原始NMEA数据 */
    char rmc[128];        // 原始RMC语句
    char gga[128];        // 原始GGA语句
} GPS_Data_t;

/* 航向处理相关结构体 */
typedef struct {
    float course_history[COURSE_HISTORY_SIZE]; // 航向历史数据
    uint8_t history_count;                     // 历史数据计数
    uint8_t history_index;                     // 历史数据索引
    float last_valid_course;                   // 最后一次有效的航向
    uint8_t has_valid_course;                  // 是否有有效航向
} Course_Data_t;

/* GPS临时存储结构体 - 用于质量1优先策略 */
typedef struct {
    GPS_Data_t temp_data;                      // 临时GPS数据
    uint8_t has_temp_data;                     // 是否有临时数据
    uint8_t temp_quality;                      // 临时数据质量
    uint32_t temp_timestamp;                   // 临时数据时间戳
} GPS_TempData_t;

/* 函数声明 */
void GPS_Init(void);
void GPS_Process(char *nmea_data);
uint8_t GPS_ParseNMEA(char *nmea_data, GPS_Data_t *gps_data);
void GPS_PrintInfo(GPS_Data_t *gps_data);
uint8_t GPS_SyncRTC(GPS_Data_t *gps_data);
char* GPS_GetPositionString(GPS_Data_t *gps_data, char *buffer);
// void GPS_StoreTempData(GPS_Data_t *gps_data); // 已简化，不再使用
float GPS_ConvertToDecimalDegrees(float nmea_format, char direction);

/* 航向处理相关函数 */
void GPS_CourseInit(void);
float GPS_ProcessCourse(float course, float speed_kmh);
float GPS_GetSmoothedCourse(void);

/* GPS数据处理和管理函数 */
void GPS_HandleNewData(char *nmea_data);
void GPS_UpdateData(void);
uint8_t GPS_IsDataReady(void);
void GPS_ClearDataReadyFlag(void);

/* GPS质量检查函数 */
uint8_t GPS_CheckQualityTimeout(uint32_t timeout_ms);
void GPS_UseTempDataIfNeeded(void);

/* 电压校准宏定义 - 根据实际测量调整 */
#define VOLTAGE_CALIBRATION_FACTOR  1.11f    // 电压校准系数，默认1.0（无校准）
#define VOLTAGE_OFFSET              0.0f    // 电压偏移量，默认0.0V

/* 温度校准宏定义 - 根据实际测量调整 */
#define TEMP_CALIBRATION_FACTOR     1.0f     // 温度校准系数，默认1.0（无校准）
#define TEMP_OFFSET                 -1.0f     // 温度偏移量，默认0.0°C（可为负值）

/* 外部变量声明 */
extern GPS_Data_t gps_data;
extern uint8_t gps_data_ready;
extern uint8_t gps_new_data;

#ifdef __cplusplus
}
#endif

#endif /* __GPS_H */
