# GSM模块使用说明

## 概述

本GSM模块代码基于Air780E GSM模块的AT指令实现，提供了完整的GSM通信功能，包括模块初始化、网络连接、数据发送等功能。

## 文件说明

### 核心文件
- `Inc/GSM.h` - GSM模块头文件，包含所有函数声明和数据结构定义
- `Src/GSM.c` - GSM模块源文件，实现所有AT指令控制功能（纯轮询方式）
- `GSM使用示例.c` - 基础使用示例代码（仅作参考，不包含在项目中）
- `GSM_FreeRTOS使用示例.c` - FreeRTOS环境下使用示例（仅作参考，不包含在项目中）

### 配置说明

#### 服务器配置
在 `GSM.h` 文件中可以修改服务器配置：
```c
#define GSM_SERVER_IP        "************"    // TCP服务器IP地址
#define GSM_SERVER_PORT      "48085"           // TCP服务器端口号
```

#### 超时时间配置
```c
#define GSM_AT_TIMEOUT_MS    5000              // AT指令超时时间(毫秒)
#define GSM_CONNECT_TIMEOUT_MS 10000           // 连接超时时间(毫秒)
```

## 设计理念

### 纯AT指令库设计
GSM模块采用**纯AT指令库**设计理念：
- **通用性**：不依赖任何操作系统，可在裸机或RTOS环境下使用
- **简洁性**：只负责AT指令的发送和接收，不包含系统相关代码
- **轮询方式**：使用轮询方式接收AT指令响应，简单可靠
- **可移植性**：易于在不同项目中移植和使用

### 在FreeRTOS中的使用
- GSM模块本身不包含FreeRTOS相关代码
- 在FreeRTOS任务中调用GSM函数
- 利用FreeRTOS的任务调度和延时功能
- 可根据需要创建专门的GSM任务

## 硬件连接

GSM模块通过LPUART1串口与STM32连接：
- 波特率：115200
- 数据位：8位
- 停止位：1位
- 校验位：无
- 流控制：无

## 主要功能

### 1. 模块初始化
```c
GSM_Status_t status = GSM_Init();
if (status == GSM_OK) {
    printf("GSM模块初始化成功\r\n");
}
```

### 2. 获取模块信息
```c
GSM_Info_t info;
GSM_Status_t status = GSM_GetInfo(&info);
if (status == GSM_OK) {
    printf("模块型号: %s\r\n", info.model);
    printf("CCID号: %s\r\n", info.ccid);
    printf("电压: %d mV\r\n", info.voltage);
    printf("信号强度: %d\r\n", info.signal);
    printf("网络注册状态: %d\r\n", info.network_reg);
}
```

### 3. 连接TCP服务器
```c
GSM_Status_t status = GSM_ConnectServer();
if (status == GSM_OK) {
    printf("服务器连接成功\r\n");
}
```

### 4. 发送数据
```c
const char* data = "Hello Server";
GSM_Status_t status = GSM_SendData(data, strlen(data));
if (status == GSM_OK) {
    printf("数据发送成功\r\n");
}
```

### 5. 关闭连接
```c
GSM_Status_t status = GSM_CloseServer();
if (status == GSM_OK) {
    printf("连接已关闭\r\n");
}
```

## AT指令支持

### 基础指令
- `ATE0` - 关闭回显
- `AT+CGMM` - 获取模块型号
- `AT+CCID` - 获取CCID号
- `AT+CBC` - 获取模块电压
- `AT+CREG?` - 查询网络注册状态
- `AT+CSQ` - 获取信号强度

### 网络指令
- `AT+CIPSTART` - 连接TCP服务器
- `AT+CIPCLOSE` - 关闭服务器连接
- `AT+CIPQSEND=1` - 设置非透传快发模式
- `AT+CIPSEND` - 发送数据

## 状态管理

### 模块状态
```c
typedef enum {
    GSM_STATE_INIT = 0,   // 初始化状态
    GSM_STATE_READY,      // 就绪状态
    GSM_STATE_CONNECTED,  // 已连接状态
    GSM_STATE_ERROR       // 错误状态
} GSM_State_t;
```

### 操作状态
```c
typedef enum {
    GSM_OK = 0,           // 指令执行成功
    GSM_ERROR,            // 指令执行失败
    GSM_TIMEOUT,          // 指令超时
    GSM_BUSY              // 模块忙
} GSM_Status_t;
```

## 使用流程

### 基本使用流程
1. 调用 `GSM_Init()` 初始化模块
2. 调用 `GSM_GetInfo()` 获取模块信息
3. 调用 `GSM_CheckNetwork()` 检查网络状态
4. 调用 `GSM_ConnectServer()` 连接服务器
5. 调用 `GSM_SetQuickSend()` 设置快发模式
6. 调用 `GSM_SendData()` 发送数据
7. 调用 `GSM_CloseServer()` 关闭连接

### 错误处理
```c
GSM_Status_t status = GSM_SomeFunction();
switch (status) {
    case GSM_OK:
        printf("操作成功\r\n");
        break;
    case GSM_ERROR:
        printf("操作失败\r\n");
        break;
    case GSM_TIMEOUT:
        printf("操作超时\r\n");
        break;
    case GSM_BUSY:
        printf("模块忙\r\n");
        break;
}
```

## 注意事项

1. **串口配置**：确保LPUART1已正确初始化，无需中断配置
2. **超时处理**：所有AT指令都有超时保护，超时后会返回 `GSM_TIMEOUT`
3. **状态检查**：发送数据前需要确保模块处于连接状态
4. **缓冲区管理**：接收缓冲区大小为256字节，注意数据长度限制
5. **错误输出**：所有错误信息使用英文输出，避免中文乱码
6. **FreeRTOS使用**：在FreeRTOS任务中使用时，注意任务优先级和堆栈大小
7. **阻塞特性**：AT指令函数是阻塞的，会占用CPU时间，建议在独立任务中运行

## 调试建议

1. **串口调试**：可以通过串口监视器查看AT指令的发送和接收
2. **状态监控**：定期检查模块状态和网络状态
3. **信号强度**：在信号较弱的环境下可能需要增加重试机制
4. **电源管理**：注意模块的电源供应，电压过低可能影响通信

## 扩展功能

可以根据需要添加以下功能：
- 短信发送功能
- 语音通话功能
- 网络状态监控
- 自动重连机制
- 数据加密传输
